import { Modu<PERSON> } from '@nestjs/common';
import { NotificationsService } from './notifications.service';
import { NotificationsController } from './notifications.controller';
import { TypeOrmModule } from '@nestjs/typeorm';
import { UsersService } from 'src/users/users.service';
import { PaginationService } from 'src/shared/pagination/pagination.service';
import {
  User,
  Notification,
  PhoneNumber,
  Address,
  Farmer,
  Buyer,
  Admin
} from 'src/users/entities/user.entity';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      User,
      Notification,
      PhoneNumber,
      Address,
      Farmer,
      Buyer,
      Admin,
    ]),
  ],
  controllers: [NotificationsController],
  providers: [
    NotificationsService,
    UsersService,
    PaginationService,
  ],
})
export class NotificationsModule {}
