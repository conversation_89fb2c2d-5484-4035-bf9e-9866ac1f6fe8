import { Module } from '@nestjs/common';
import { PaymentsService } from './payments.service';
import { PaymentsController } from './payments.controller';
import { TypeOrmModule } from '@nestjs/typeorm';
import {
  User,
  Order,
  Transaction,
  Wallet,
  Cart,
  CartItem,
  Farmer,
  Buyer,
  Admin,
  Produce,
  Category,
  Address,
  Preferences,
  Notification
} from '../users/entities/user.entity';
import { OrdersService } from 'src/orders/orders.service';
import { TransactionsService } from 'src/transactions/transactions.service';
import { UsersService } from 'src/users/users.service';
import { WalletsService } from 'src/wallets/wallets.service';
import { CartService } from 'src/cart/cart.service';
import { PreferencesService } from 'src/preferences/preferences.service';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      User,
      Farmer,
      Buyer,
      Admin,
      Order,
      Transaction,
      Wallet,
      Cart,
      CartItem,
      Produce,
      Category,
      Address,
      Preferences,
      Notification
    ]),
  ],
  controllers: [PaymentsController],
  providers: [
    PaymentsService,
    OrdersService,
    TransactionsService,
    UsersService,
    WalletsService,
    CartService,
    PreferencesService,
  ],
  exports: [PaymentsService, TypeOrmModule],
})
export class PaymentsModule {}
