import { Module } from '@nestjs/common';
import { PaymentsService } from './payments.service';
import { PaymentsController } from './payments.controller';
import { TypeOrmModule } from '@nestjs/typeorm';
import {
  User,
  Farmer,
  Buyer,
  Admin,
  Address,
  Preferences,
  Notification,
  PhoneNumber
} from '../users/entities/user.entity';
import { Order, OrderItem } from '../orders/entities/order.entity';
import { Transaction } from '../transactions/entities/transaction.entity';
import { Wallet } from '../wallets/entities/wallet.entity';
import { Cart, CartItem } from '../cart/entities/cart.entity';
import { Produce } from '../produce/entities/produce.entity';
import { Category } from '../category/entities/category.entity';
import { OrdersService } from 'src/orders/orders.service';
import { TransactionsService } from 'src/transactions/transactions.service';
import { UsersService } from 'src/users/users.service';
import { WalletsService } from 'src/wallets/wallets.service';
import { CartService } from 'src/cart/cart.service';
import { PreferencesService } from 'src/preferences/preferences.service';
import { PaginationService } from 'src/shared/pagination/pagination.service';
import { ProduceService } from 'src/produce/produce.service';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      User,
      Farmer,
      Buyer,
      Admin,
      Order,
      OrderItem,
      Transaction,
      Wallet,
      Cart,
      CartItem,
      Produce,
      Category,
      Address,
      Preferences,
      Notification,
      PhoneNumber
    ]),
  ],
  controllers: [PaymentsController],
  providers: [
    PaymentsService,
    OrdersService,
    TransactionsService,
    UsersService,
    WalletsService,
    CartService,
    PreferencesService,
    PaginationService,
    ProduceService,
  ],
  exports: [PaymentsService, TypeOrmModule],
})
export class PaymentsModule {}
