import {
  BadRequestException,
  ConflictException,
  Injectable,
  NotFoundException,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { User } from '../users/entities/user.entity';
import { Cart, CartItem } from './entities/cart.entity';
import { Produce } from '../produce/entities/produce.entity';
import { AddToCartDto } from './dto/create-cart.dto';
import { UpdateCartItemDto } from './dto/update-cart.dto';
import { PaginationQueryDto } from 'src/shared/pagination/pagination-query.dto';
import { PaginationService } from 'src/shared/pagination/pagination.service';
import { UsersService } from 'src/users/users.service';

@Injectable()
export class CartService {
  constructor(
    @InjectRepository(Cart)
    private cartRepository: Repository<Cart>,
    @InjectRepository(CartItem)
    private cartItemRepository: Repository<CartItem>,
    @InjectRepository(Produce)
    private produceRepository: Repository<Produce>,
    @InjectRepository(User)
    private userRepository: Repository<User>,
  ) {}

  /**
   * Get cart by buyer ID or create new one if it doesn't exist
   */
  async getOrCreateCart(buyerId: string): Promise<Cart> {
    // Find existing cart or create a new one
    let cart = await this.cartRepository.findOne({
      where: { buyer_id: buyerId },
      relations: ['items']
    });

    if (!cart) {
      cart = this.cartRepository.create({
        buyer_id: buyerId,
        total_amount: 0,
      });
      cart = await this.cartRepository.save(cart);
    }

    return cart;
  }

  /**
   * Get cart by buyer ID
   */
  async getCartByBuyerId(buyerId: string): Promise<Cart> {
    const cart = await this.cartRepository.findOne({
      where: { buyer_id: buyerId },
      relations: ['items', 'items.produce']
    });

    if (!cart) {
      throw new NotFoundException(`Cart not found for buyer ${buyerId}`);
    }

    return cart;
  }

  /**
   * Add item to cart
   */
  async addItem(buyerId: string, itemDto: AddToCartDto): Promise<Cart> {
    const { produceId, quantity, price } = itemDto;

    if (quantity <= 0) {
      throw new BadRequestException('Quantity must be greater than 0');
    }

    if (price < 0) {
      throw new BadRequestException('Price cannot be negative');
    }

    // Verify produce exists
    const produce = await this.produceRepository.findOne({ where: { id: produceId } });
    if (!produce) {
      throw new NotFoundException('Produce not found');
    }

    const cart = await this.getOrCreateCart(buyerId);

    // Check if item already exists in cart
    const existingItem = await this.cartItemRepository.findOne({
      where: { cart_id: cart.id, produce_id: produceId }
    });

    const totalPrice = quantity * price;

    if (existingItem) {
      // Update existing item
      existingItem.quantity += quantity;
      existingItem.total_price += totalPrice;
      await this.cartItemRepository.save(existingItem);
    } else {
      // Add new item
      const newItem = this.cartItemRepository.create({
        cart_id: cart.id,
        produce_id: produceId,
        quantity,
        unit_price: price,
        total_price: totalPrice,
      });
      await this.cartItemRepository.save(newItem);
    }

    // Update cart total cost
    const cartItems = await this.cartItemRepository.find({ where: { cart_id: cart.id } });
    cart.total_amount = cartItems.reduce((sum, item) => sum + item.total_price, 0);
    await this.cartRepository.save(cart);

    return this.getCartByBuyerId(buyerId);
  }

  /**
   * Update item quantity in cart
   */
  async updateItemQuantity(
    buyerId: string,
    updateDto: UpdateCartItemDto,
  ): Promise<Cart> {
    const { produceId, quantity } = updateDto;

    if (quantity < 0) {
      throw new BadRequestException('Quantity cannot be negative');
    }

    const cart = await this.getCartByBuyerId(buyerId);

    // Find item in cart
    const cartItem = await this.cartItemRepository.findOne({
      where: { cart_id: cart.id, produce_id: produceId }
    });

    if (!cartItem) {
      throw new NotFoundException(
        `Item with ID ${produceId} not found in cart`,
      );
    }

    if (quantity === 0) {
      // Remove item if quantity is 0
      return this.removeItem(buyerId, cartItem.id);
    }

    // Calculate new total price based on unit price
    const unitPrice = cartItem.unit_price;
    const newTotalPrice = quantity * unitPrice;

    // Update item
    cartItem.quantity = quantity;
    cartItem.total_price = newTotalPrice;
    await this.cartItemRepository.save(cartItem);

    // Update cart total cost
    const cartItems = await this.cartItemRepository.find({ where: { cart_id: cart.id } });
    cart.total_amount = cartItems.reduce((sum, item) => sum + item.total_price, 0);
    await this.cartRepository.save(cart);

    return this.getCartByBuyerId(buyerId);
  }

  /**
   * Remove item from cart
   */
  async removeItem(buyerId: string, itemId: string): Promise<Cart> {
    const cart = await this.getCartByBuyerId(buyerId);

    // Find and remove the cart item
    const cartItem = await this.cartItemRepository.findOne({
      where: { id: itemId, cart_id: cart.id }
    });

    if (!cartItem) {
      throw new NotFoundException(`Item with ID ${itemId} not found in cart`);
    }

    // Remove item
    await this.cartItemRepository.remove(cartItem);

    // Recalculate total cost
    const remainingItems = await this.cartItemRepository.find({ where: { cart_id: cart.id } });
    cart.total_amount = remainingItems.reduce((sum, item) => sum + item.total_price, 0);
    await this.cartRepository.save(cart);

    return this.getCartByBuyerId(buyerId);
  }

  /**
   * Clear all items from cart
   */
  async clearCart(buyerId: string): Promise<Cart> {
    const cart = await this.getCartByBuyerId(buyerId);

    // Remove all cart items
    await this.cartItemRepository.delete({ cart_id: cart.id });

    // Update cart total
    cart.total_amount = 0;
    await this.cartRepository.save(cart);

    return cart;
  }

  /**
   * Delete cart
   */
  async deleteCart(buyerId: string): Promise<{ deleted: boolean }> {
    const cart = await this.cartRepository.findOne({ where: { buyer_id: buyerId } });

    if (!cart) {
      return { deleted: false };
    }

    // Remove all cart items first
    await this.cartItemRepository.delete({ cart_id: cart.id });

    // Remove cart
    const result = await this.cartRepository.remove(cart);

    return { deleted: !!result };
  }
}
