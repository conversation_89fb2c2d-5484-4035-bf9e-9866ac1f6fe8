import { Modu<PERSON> } from '@nestjs/common';
import { CartService } from './cart.service';
import { CartController } from './cart.controller';
import { TypeOrmModule } from '@nestjs/typeorm';
import { User, Cart, CartItem, Produce } from '../users/entities/user.entity';



@Module({
  imports: [
    TypeOrmModule.forFeature([User, Cart, CartItem, Produce]),
  ],
  controllers: [CartController],
  providers: [CartService],
  exports: [CartService, TypeOrmModule],
})
export class CartModule {}
