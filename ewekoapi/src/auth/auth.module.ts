import { Module } from '@nestjs/common';
import { AuthService } from './auth.service';
import { AuthController } from './auth.controller';
import { UsersService } from 'src/users/users.service';
import { JwtService } from '@nestjs/jwt';
import { TypeOrmModule } from '@nestjs/typeorm';

import {
  User,
  Profile,
  Contact,
  Business,
  Farmer,
  Buyer,
  Admin,
  Address,
  Notification,
  PhoneNumber,
} from '../users/entities/user.entity';
import { UserType } from 'src/shared/enums';
import { OtpsService } from 'src/otps/otps.service';
import { Otp } from '../otp/entities/otp.entity';
import { CartService } from 'src/cart/cart.service';
import { Cart, CartItem } from '../cart/entities/cart.entity';
import { Produce } from '../produce/entities/produce.entity';
import { PaginationService } from 'src/shared/pagination/pagination.service';
import { Wallet } from '../wallets/entities/wallet.entity';
import { Order } from '../orders/entities/order.entity';
import { WalletsService } from 'src/wallets/wallets.service';
import { Preferences } from '../preferences/entities/preferences.entity';
import { PreferencesService } from 'src/preferences/preferences.service';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      User,
      Profile,
      Contact,
      Business,
      Farmer,
      Buyer,
      Admin,
      Address,
      Notification,
      PhoneNumber,
      Otp,
      Cart,
      CartItem,
      Produce,
      Wallet,
      Order,
      Preferences,
    ]),
  ],
  controllers: [AuthController],
  providers: [
    AuthService,
    UsersService,
    JwtService,
    OtpsService,
    CartService,
    PaginationService,
    WalletsService,
    PreferencesService,
  ],
})
export class AuthModule {}
