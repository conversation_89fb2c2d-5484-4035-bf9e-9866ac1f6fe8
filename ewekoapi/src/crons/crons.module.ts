import { Module } from '@nestjs/common';
import { CronsService } from './crons.service';
import { TypeOrmModule } from '@nestjs/typeorm';
import { User, Otp, Notification } from '../users/entities/user.entity';
import { WeeklyPriceModule } from '../weekly-price/weekly-price.module';

@Module({
  imports: [
    WeeklyPriceModule,
    TypeOrmModule.forFeature([User, Otp, Notification]),
  ],
  controllers: [],
  providers: [CronsService],
  exports: [CronsService, TypeOrmModule],
})
export class CronsModule {}
