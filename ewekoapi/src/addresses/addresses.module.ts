import { Module } from '@nestjs/common';
import { AddressesService } from './addresses.service';
import { AddressesController } from './addresses.controller';
import { TypeOrmModule } from '@nestjs/typeorm';
import { User, Address } from '../users/entities/user.entity';

@Module({
  imports: [
    TypeOrmModule.forFeature([User, Address]),
  ],
  controllers: [AddressesController],
  providers: [AddressesService],
  exports: [AddressesService, TypeOrmModule],
})
export class AddressesModule {}
