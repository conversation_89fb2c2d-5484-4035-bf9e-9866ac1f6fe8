import { MigrationInterface, QueryRunner } from "typeorm";

export class InitialSchema1752072800000 implements MigrationInterface {
    name = 'InitialSchema1752072800000'

    public async up(queryRunner: QueryRunner): Promise<void> {
        // Create UUID extension
        await queryRunner.query(`CREATE EXTENSION IF NOT EXISTS "uuid-ossp"`);
        
        // Create enums
        await queryRunner.query(`CREATE TYPE "public"."users_type_enum" AS ENUM('BUYER', 'FARMER', 'ADMIN', 'AGENT')`);
        await queryRunner.query(`CREATE TYPE "public"."users_gender_enum" AS ENUM('male', 'female', 'other')`);
        await queryRunner.query(`CREATE TYPE "public"."admins_role_enum" AS ENUM('SUPER_ADMIN', 'SUB_ADMIN')`);
        await queryRunner.query(`CREATE TYPE "public"."phone_numbers_type_enum" AS ENUM('personal', 'business', 'emergency', 'other')`);
        await queryRunner.query(`CREATE TYPE "public"."addresses_type_enum" AS ENUM('home', 'work', 'delivery', 'billing', 'other')`);
        await queryRunner.query(`CREATE TYPE "public"."notifications_type_enum" AS ENUM('info', 'warning', 'error', 'success')`);
        await queryRunner.query(`CREATE TYPE "public"."orders_status_enum" AS ENUM('pending', 'confirmed', 'processing', 'shipped', 'delivered', 'cancelled')`);
        await queryRunner.query(`CREATE TYPE "public"."orders_payment_status_enum" AS ENUM('pending', 'paid', 'failed', 'refunded')`);
        await queryRunner.query(`CREATE TYPE "public"."transactions_status_enum" AS ENUM('pending', 'completed', 'failed', 'cancelled')`);
        await queryRunner.query(`CREATE TYPE "public"."transactions_payment_method_enum" AS ENUM('card', 'bank_transfer', 'wallet', 'cash')`);
        await queryRunner.query(`CREATE TYPE "public"."otps_use_case_enum" AS ENUM('LOGIN', '2FA', 'D2FA', 'VRFY_EMAIL', 'VRFY_PHONE', 'PWDR')`);

        // Create users table
        await queryRunner.query(`
            CREATE TABLE "users" (
                "id" uuid NOT NULL DEFAULT uuid_generate_v4(),
                "username" character varying NOT NULL,
                "password" character varying NOT NULL,
                "type" "public"."users_type_enum" NOT NULL,
                "verified" boolean NOT NULL DEFAULT false,
                "is_active" boolean NOT NULL DEFAULT true,
                "last_login" TIMESTAMP,
                "first_name" character varying NOT NULL,
                "last_name" character varying NOT NULL,
                "middle_name" character varying,
                "prefix" character varying,
                "gender" "public"."users_gender_enum",
                "date_of_birth" date,
                "profile_picture" character varying,
                "email" character varying NOT NULL,
                "business_name" character varying,
                "is_premium" boolean NOT NULL DEFAULT false,
                "is_phone_verified" boolean NOT NULL DEFAULT false,
                "is_email_verified" boolean NOT NULL DEFAULT false,
                "created_at" TIMESTAMP NOT NULL DEFAULT now(),
                "updated_at" TIMESTAMP NOT NULL DEFAULT now(),
                CONSTRAINT "UQ_fe0bb3f6520ee0469504521e710" UNIQUE ("username"),
                CONSTRAINT "UQ_97672ac88f789774dd47f7c8be3" UNIQUE ("email"),
                CONSTRAINT "PK_a3ffb1c0c8416b9fc6f907b7433" PRIMARY KEY ("id")
            )
        `);

        // Create farmers table
        await queryRunner.query(`
            CREATE TABLE "farmers" (
                "id" uuid NOT NULL DEFAULT uuid_generate_v4(),
                "user_id" uuid NOT NULL,
                "farm_name" character varying,
                "farm_location" json,
                "farm_size" character varying,
                "farm_address" character varying,
                "account_number" character varying,
                "account_name" character varying,
                "bank_name" character varying,
                "bank_branch" character varying,
                "created_at" TIMESTAMP NOT NULL DEFAULT now(),
                "updated_at" TIMESTAMP NOT NULL DEFAULT now(),
                CONSTRAINT "UQ_f2598912e9b3fd889f6fecee93f" UNIQUE ("user_id"),
                CONSTRAINT "REL_f2598912e9b3fd889f6fecee93" UNIQUE ("user_id"),
                CONSTRAINT "PK_ccbe91e5e64dde1329b4c153637" PRIMARY KEY ("id")
            )
        `);

        // Create buyers table
        await queryRunner.query(`
            CREATE TABLE "buyers" (
                "id" uuid NOT NULL DEFAULT uuid_generate_v4(),
                "user_id" uuid NOT NULL,
                "loyalty_points" integer NOT NULL DEFAULT '0',
                "delivery_preferences" json,
                "payment_methods" json,
                "created_at" TIMESTAMP NOT NULL DEFAULT now(),
                "updated_at" TIMESTAMP NOT NULL DEFAULT now(),
                CONSTRAINT "UQ_00d0563e17355f153e8a05fbc20" UNIQUE ("user_id"),
                CONSTRAINT "REL_00d0563e17355f153e8a05fbc2" UNIQUE ("user_id"),
                CONSTRAINT "PK_aff372821d05bac04a18ff8eb87" PRIMARY KEY ("id")
            )
        `);

        // Create admins table
        await queryRunner.query(`
            CREATE TABLE "admins" (
                "id" uuid NOT NULL DEFAULT uuid_generate_v4(),
                "user_id" uuid NOT NULL,
                "role" "public"."admins_role_enum" NOT NULL,
                "permissions" json,
                "managed_departments" json,
                "admin_token" character varying,
                "created_at" TIMESTAMP NOT NULL DEFAULT now(),
                "updated_at" TIMESTAMP NOT NULL DEFAULT now(),
                CONSTRAINT "UQ_2b901dd818a2a6486994d915a68" UNIQUE ("user_id"),
                CONSTRAINT "REL_2b901dd818a2a6486994d915a6" UNIQUE ("user_id"),
                CONSTRAINT "PK_e3b38270c97a854c48d2e80874e" PRIMARY KEY ("id")
            )
        `);

        // Create phone_numbers table
        await queryRunner.query(`
            CREATE TABLE "phone_numbers" (
                "id" uuid NOT NULL DEFAULT uuid_generate_v4(),
                "user_id" uuid NOT NULL,
                "phone_number" character varying NOT NULL,
                "type" "public"."phone_numbers_type_enum" NOT NULL DEFAULT 'personal',
                "is_primary" boolean NOT NULL DEFAULT false,
                "is_verified" boolean NOT NULL DEFAULT false,
                "created_at" TIMESTAMP NOT NULL DEFAULT now(),
                "updated_at" TIMESTAMP NOT NULL DEFAULT now(),
                CONSTRAINT "PK_a72cf9a1834a1417e195fdd2c02" PRIMARY KEY ("id")
            )
        `);

        // Create addresses table
        await queryRunner.query(`
            CREATE TABLE "addresses" (
                "id" uuid NOT NULL DEFAULT uuid_generate_v4(),
                "user_id" uuid NOT NULL,
                "street" character varying NOT NULL,
                "city" character varying NOT NULL,
                "state" character varying NOT NULL,
                "country" character varying NOT NULL,
                "postal_code" character varying,
                "type" "public"."addresses_type_enum" NOT NULL DEFAULT 'home',
                "is_primary" boolean NOT NULL DEFAULT false,
                "created_at" TIMESTAMP NOT NULL DEFAULT now(),
                "updated_at" TIMESTAMP NOT NULL DEFAULT now(),
                CONSTRAINT "PK_745d8f43d3af10ab8247465e450" PRIMARY KEY ("id")
            )
        `);

        // Create categories table
        await queryRunner.query(`
            CREATE TABLE "categories" (
                "id" uuid NOT NULL DEFAULT uuid_generate_v4(),
                "name" character varying NOT NULL,
                "slug" character varying NOT NULL,
                "description" character varying,
                "image" character varying,
                "is_active" boolean NOT NULL DEFAULT true,
                "created_at" TIMESTAMP NOT NULL DEFAULT now(),
                "updated_at" TIMESTAMP NOT NULL DEFAULT now(),
                CONSTRAINT "UQ_8b0be371d28245da6e4f4b61878" UNIQUE ("name"),
                CONSTRAINT "UQ_420d9f679d41281f282f5bc7d09" UNIQUE ("slug"),
                CONSTRAINT "PK_24dbc6126a28ff948da33e97d3b" PRIMARY KEY ("id")
            )
        `);

        // Create produce table
        await queryRunner.query(`
            CREATE TABLE "produce" (
                "id" uuid NOT NULL DEFAULT uuid_generate_v4(),
                "name" character varying NOT NULL,
                "slug" character varying NOT NULL,
                "description" character varying,
                "farmer_id" uuid NOT NULL,
                "category_id" uuid,
                "price" numeric(10,2) NOT NULL,
                "quantity" numeric(10,2) NOT NULL DEFAULT '0',
                "unit" character varying,
                "images" json,
                "is_available" boolean NOT NULL DEFAULT true,
                "is_active" boolean NOT NULL DEFAULT true,
                "created_at" TIMESTAMP NOT NULL DEFAULT now(),
                "updated_at" TIMESTAMP NOT NULL DEFAULT now(),
                CONSTRAINT "UQ_01f5538475d7c90618eb3b6bfac" UNIQUE ("slug"),
                CONSTRAINT "PK_d5ec91774896882b737846e75b3" PRIMARY KEY ("id")
            )
        `);

        // Create orders table
        await queryRunner.query(`
            CREATE TABLE "orders" (
                "id" uuid NOT NULL DEFAULT uuid_generate_v4(),
                "buyer_id" uuid NOT NULL,
                "farmer_id" uuid NOT NULL,
                "total_amount" numeric(10,2) NOT NULL,
                "status" "public"."orders_status_enum" NOT NULL,
                "payment_status" "public"."orders_payment_status_enum" NOT NULL,
                "delivery_address" character varying,
                "notes" character varying,
                "created_at" TIMESTAMP NOT NULL DEFAULT now(),
                "updated_at" TIMESTAMP NOT NULL DEFAULT now(),
                CONSTRAINT "PK_710e2d4957aa5878dfe94e4ac2f" PRIMARY KEY ("id")
            )
        `);

        // Create transactions table
        await queryRunner.query(`
            CREATE TABLE "transactions" (
                "id" uuid NOT NULL DEFAULT uuid_generate_v4(),
                "order_id" uuid NOT NULL,
                "buyer_id" uuid NOT NULL,
                "farmer_id" uuid NOT NULL,
                "amount" numeric(10,2) NOT NULL,
                "status" "public"."transactions_status_enum" NOT NULL,
                "payment_method" "public"."transactions_payment_method_enum" NOT NULL,
                "payment_reference" character varying,
                "description" character varying,
                "created_at" TIMESTAMP NOT NULL DEFAULT now(),
                "updated_at" TIMESTAMP NOT NULL DEFAULT now(),
                CONSTRAINT "PK_a219afd8dd77ed80f5a862f1db9" PRIMARY KEY ("id")
            )
        `);

        // Create wallets table
        await queryRunner.query(`
            CREATE TABLE "wallets" (
                "id" uuid NOT NULL DEFAULT uuid_generate_v4(),
                "farmer_id" uuid NOT NULL,
                "balance" numeric(10,2) NOT NULL DEFAULT '0',
                "pending_balance" numeric(10,2) NOT NULL DEFAULT '0',
                "active" boolean NOT NULL DEFAULT true,
                "created_at" TIMESTAMP NOT NULL DEFAULT now(),
                "updated_at" TIMESTAMP NOT NULL DEFAULT now(),
                CONSTRAINT "REL_d701bd1aa0b3bc2ab91d28d69c" UNIQUE ("farmer_id"),
                CONSTRAINT "PK_8402e5df5a30a229380e83e4f7e" PRIMARY KEY ("id")
            )
        `);

        // Create carts table
        await queryRunner.query(`
            CREATE TABLE "carts" (
                "id" uuid NOT NULL DEFAULT uuid_generate_v4(),
                "buyer_id" uuid NOT NULL,
                "total_amount" numeric(10,2) NOT NULL DEFAULT '0',
                "active" boolean NOT NULL DEFAULT true,
                "created_at" TIMESTAMP NOT NULL DEFAULT now(),
                "updated_at" TIMESTAMP NOT NULL DEFAULT now(),
                CONSTRAINT "PK_b5f695a59f5ebb50af3c8160816" PRIMARY KEY ("id")
            )
        `);

        // Create cart_items table
        await queryRunner.query(`
            CREATE TABLE "cart_items" (
                "id" uuid NOT NULL DEFAULT uuid_generate_v4(),
                "cart_id" uuid NOT NULL,
                "produce_id" uuid NOT NULL,
                "quantity" integer NOT NULL,
                "unit_price" numeric(10,2) NOT NULL,
                "total_price" numeric(10,2) NOT NULL,
                "created_at" TIMESTAMP NOT NULL DEFAULT now(),
                "updated_at" TIMESTAMP NOT NULL DEFAULT now(),
                CONSTRAINT "PK_6fccf5ec03c172d27a28a82928b" PRIMARY KEY ("id")
            )
        `);

        // Create order_items table
        await queryRunner.query(`
            CREATE TABLE "order_items" (
                "id" uuid NOT NULL DEFAULT uuid_generate_v4(),
                "order_id" uuid NOT NULL,
                "produce_id" uuid NOT NULL,
                "quantity" integer NOT NULL,
                "unit_price" numeric(10,2) NOT NULL,
                "total_price" numeric(10,2) NOT NULL,
                "created_at" TIMESTAMP NOT NULL DEFAULT now(),
                "updated_at" TIMESTAMP NOT NULL DEFAULT now(),
                CONSTRAINT "PK_005269d8574e6fac0493715c308" PRIMARY KEY ("id")
            )
        `);

        // Create preferences table
        await queryRunner.query(`
            CREATE TABLE "preferences" (
                "id" uuid NOT NULL DEFAULT uuid_generate_v4(),
                "user_id" uuid NOT NULL,
                "email_notifications" boolean NOT NULL DEFAULT true,
                "sms_notifications" boolean NOT NULL DEFAULT true,
                "push_notifications" boolean NOT NULL DEFAULT true,
                "language" character varying NOT NULL DEFAULT 'en',
                "theme" character varying NOT NULL DEFAULT 'light',
                "notification_preferences" json,
                "created_at" TIMESTAMP NOT NULL DEFAULT now(),
                "updated_at" TIMESTAMP NOT NULL DEFAULT now(),
                CONSTRAINT "REL_34a542d34f1c75c43e78df2e67" UNIQUE ("user_id"),
                CONSTRAINT "PK_17f8855e4145192bbabd91a51be" PRIMARY KEY ("id")
            )
        `);

        // Create notifications table
        await queryRunner.query(`
            CREATE TABLE "notifications" (
                "id" uuid NOT NULL DEFAULT uuid_generate_v4(),
                "user_id" uuid NOT NULL,
                "title" character varying NOT NULL,
                "message" character varying NOT NULL,
                "read" boolean NOT NULL DEFAULT false,
                "type" "public"."notifications_type_enum" NOT NULL,
                "created_at" TIMESTAMP NOT NULL DEFAULT now(),
                "updated_at" TIMESTAMP NOT NULL DEFAULT now(),
                CONSTRAINT "PK_6a72c3c0f683f6462415e653c3a" PRIMARY KEY ("id")
            )
        `);

        // Create otps table
        await queryRunner.query(`
            CREATE TABLE "otps" (
                "id" uuid NOT NULL DEFAULT uuid_generate_v4(),
                "user_id" uuid NOT NULL,
                "code" character varying NOT NULL,
                "use_case" "public"."otps_use_case_enum" NOT NULL,
                "used" boolean NOT NULL DEFAULT false,
                "expires_at" TIMESTAMP NOT NULL,
                "created_at" TIMESTAMP NOT NULL DEFAULT now(),
                "updated_at" TIMESTAMP NOT NULL DEFAULT now(),
                CONSTRAINT "PK_91fef5ed60605b854a2115d2410" PRIMARY KEY ("id")
            )
        `);

        // Add foreign key constraints
        await queryRunner.query(`ALTER TABLE "farmers" ADD CONSTRAINT "FK_f2598912e9b3fd889f6fecee93f" FOREIGN KEY ("user_id") REFERENCES "users"("id") ON DELETE CASCADE ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "buyers" ADD CONSTRAINT "FK_00d0563e17355f153e8a05fbc20" FOREIGN KEY ("user_id") REFERENCES "users"("id") ON DELETE CASCADE ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "admins" ADD CONSTRAINT "FK_2b901dd818a2a6486994d915a68" FOREIGN KEY ("user_id") REFERENCES "users"("id") ON DELETE CASCADE ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "phone_numbers" ADD CONSTRAINT "FK_phone_numbers_user_id" FOREIGN KEY ("user_id") REFERENCES "users"("id") ON DELETE CASCADE ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "addresses" ADD CONSTRAINT "FK_addresses_user_id" FOREIGN KEY ("user_id") REFERENCES "users"("id") ON DELETE CASCADE ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "notifications" ADD CONSTRAINT "FK_notifications_user_id" FOREIGN KEY ("user_id") REFERENCES "users"("id") ON DELETE CASCADE ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "produce" ADD CONSTRAINT "FK_produce_farmer_id" FOREIGN KEY ("farmer_id") REFERENCES "farmers"("user_id") ON DELETE CASCADE ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "produce" ADD CONSTRAINT "FK_produce_category_id" FOREIGN KEY ("category_id") REFERENCES "categories"("id") ON DELETE SET NULL ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "orders" ADD CONSTRAINT "FK_orders_buyer_id" FOREIGN KEY ("buyer_id") REFERENCES "buyers"("user_id") ON DELETE CASCADE ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "orders" ADD CONSTRAINT "FK_orders_farmer_id" FOREIGN KEY ("farmer_id") REFERENCES "farmers"("user_id") ON DELETE CASCADE ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "transactions" ADD CONSTRAINT "FK_transactions_order_id" FOREIGN KEY ("order_id") REFERENCES "orders"("id") ON DELETE CASCADE ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "transactions" ADD CONSTRAINT "FK_transactions_buyer_id" FOREIGN KEY ("buyer_id") REFERENCES "buyers"("user_id") ON DELETE CASCADE ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "transactions" ADD CONSTRAINT "FK_transactions_farmer_id" FOREIGN KEY ("farmer_id") REFERENCES "farmers"("user_id") ON DELETE CASCADE ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "wallets" ADD CONSTRAINT "FK_d701bd1aa0b3bc2ab91d28d69c5" FOREIGN KEY ("farmer_id") REFERENCES "farmers"("user_id") ON DELETE CASCADE ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "carts" ADD CONSTRAINT "FK_carts_buyer_id" FOREIGN KEY ("buyer_id") REFERENCES "buyers"("user_id") ON DELETE CASCADE ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "cart_items" ADD CONSTRAINT "FK_cart_items_cart_id" FOREIGN KEY ("cart_id") REFERENCES "carts"("id") ON DELETE CASCADE ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "cart_items" ADD CONSTRAINT "FK_cart_items_produce_id" FOREIGN KEY ("produce_id") REFERENCES "produce"("id") ON DELETE CASCADE ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "order_items" ADD CONSTRAINT "FK_order_items_order_id" FOREIGN KEY ("order_id") REFERENCES "orders"("id") ON DELETE CASCADE ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "order_items" ADD CONSTRAINT "FK_order_items_produce_id" FOREIGN KEY ("produce_id") REFERENCES "produce"("id") ON DELETE CASCADE ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "preferences" ADD CONSTRAINT "FK_34a542d34f1c75c43e78df2e67b" FOREIGN KEY ("user_id") REFERENCES "users"("id") ON DELETE CASCADE ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "otps" ADD CONSTRAINT "FK_otps_user_id" FOREIGN KEY ("user_id") REFERENCES "users"("id") ON DELETE CASCADE ON UPDATE NO ACTION`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`DROP TABLE "otps"`);
        await queryRunner.query(`DROP TABLE "notifications"`);
        await queryRunner.query(`DROP TABLE "preferences"`);
        await queryRunner.query(`DROP TABLE "order_items"`);
        await queryRunner.query(`DROP TABLE "cart_items"`);
        await queryRunner.query(`DROP TABLE "carts"`);
        await queryRunner.query(`DROP TABLE "wallets"`);
        await queryRunner.query(`DROP TABLE "transactions"`);
        await queryRunner.query(`DROP TABLE "orders"`);
        await queryRunner.query(`DROP TABLE "produce"`);
        await queryRunner.query(`DROP TABLE "categories"`);
        await queryRunner.query(`DROP TABLE "addresses"`);
        await queryRunner.query(`DROP TABLE "phone_numbers"`);
        await queryRunner.query(`DROP TABLE "admins"`);
        await queryRunner.query(`DROP TABLE "buyers"`);
        await queryRunner.query(`DROP TABLE "farmers"`);
        await queryRunner.query(`DROP TABLE "users"`);
        
        await queryRunner.query(`DROP TYPE "public"."otps_use_case_enum"`);
        await queryRunner.query(`DROP TYPE "public"."transactions_payment_method_enum"`);
        await queryRunner.query(`DROP TYPE "public"."transactions_status_enum"`);
        await queryRunner.query(`DROP TYPE "public"."orders_payment_status_enum"`);
        await queryRunner.query(`DROP TYPE "public"."orders_status_enum"`);
        await queryRunner.query(`DROP TYPE "public"."notifications_type_enum"`);
        await queryRunner.query(`DROP TYPE "public"."addresses_type_enum"`);
        await queryRunner.query(`DROP TYPE "public"."phone_numbers_type_enum"`);
        await queryRunner.query(`DROP TYPE "public"."admins_role_enum"`);
        await queryRunner.query(`DROP TYPE "public"."users_gender_enum"`);
        await queryRunner.query(`DROP TYPE "public"."users_type_enum"`);
    }
}
