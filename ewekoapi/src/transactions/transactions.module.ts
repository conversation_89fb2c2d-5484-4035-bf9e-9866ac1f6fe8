import { Module } from '@nestjs/common';
import { TransactionsService } from './transactions.service';
import { TransactionsController } from './transactions.controller';
import { TypeOrmModule } from '@nestjs/typeorm';
import { User, Order, Transaction } from '../users/entities/user.entity';
import { UsersService } from 'src/users/users.service';

@Module({
  imports: [
    TypeOrmModule.forFeature([User, Order, Transaction]),
  ],
  controllers: [TransactionsController],
  providers: [TransactionsService, UsersService],
  exports: [TransactionsService, TypeOrmModule],
})
export class TransactionsModule {}
