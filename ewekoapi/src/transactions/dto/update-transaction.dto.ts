import { ApiProperty } from '@nestjs/swagger';
import { PaymentStatus } from 'src/shared/enums';
import { IsBoolean, IsEnum, IsNotEmpty, IsOptional } from 'class-validator';

export class UpdateTransactionDto {
  @ApiProperty({
    description: 'Payment method used',
    enum: PaymentStatus,
    example: PaymentStatus.COMPLETED,
  })
  @IsEnum(PaymentStatus)
  @IsNotEmpty()
  status: PaymentStatus;

  @ApiProperty({
    description: 'Indicates whether the transaction has been processed',
    example: true,
    default: false,
    required: false,
  })
  @IsBoolean()
  @IsNotEmpty()
  @IsOptional()
  processed?: boolean;
}
