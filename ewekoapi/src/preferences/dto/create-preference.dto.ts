import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import {
  IsBoolean,
  IsEnum,
  IsOptional,
  IsString,
  IsUUID,
} from 'class-validator';

// Create DTO
export class CreatePreferencesDto {
  @ApiProperty({
    description: 'User ID reference',
    type: String,
    required: true,
    example: 'f47ac10b-58cc-4372-a567-0e02b2c3d479',
  })
  @IsUUID()
  user: string;
}

// Update DTO
enum NotificationChannel {
  EMAIL = 'EMAIL',
  SMS = 'SMS',
  BOTH = 'BOTH',
}

export class UpdatePreferencesDto {
  @ApiPropertyOptional({
    description: 'Preferred destination for OTP codes',
    enum: NotificationChannel,
    example: 'BOTH',
  })
  @IsEnum(NotificationChannel)
  @IsOptional()
  otpDestination?: NotificationChannel;

  @ApiPropertyOptional({
    description: 'Receive promotional emails or messages',
    example: false,
  })
  @IsBoolean()
  @IsOptional()
  receivePromotions?: boolean;

  @ApiPropertyOptional({
    description: 'Enable or disable 2FA',
    example: false,
  })
  @IsBoolean()
  @IsOptional()
  enable2fa?: boolean;

  @ApiPropertyOptional({
    description: 'Enable or disable general update notifications',
    example: true,
  })
  @IsBoolean()
  @IsOptional()
  generalUpdates?: boolean;

  @ApiPropertyOptional({
    description: 'Enable or disable order update notifications',
    example: true,
  })
  @IsBoolean()
  @IsOptional()
  orderUpdates?: boolean;

  @ApiPropertyOptional({
    description: 'Enable or disable transaction update notifications',
    example: true,
  })
  @IsBoolean()
  @IsOptional()
  transactionUpdates?: boolean;

  @ApiPropertyOptional({
    description: 'Enable or disable payment update notifications',
    example: true,
  })
  @IsBoolean()
  @IsOptional()
  paymentUpdates?: boolean;

  @ApiPropertyOptional({
    description: 'Enable or disable delivery update notifications',
    example: true,
  })
  @IsBoolean()
  @IsOptional()
  deliveryUpdates?: boolean;
}

export class PreferencesResponseDto {
  @ApiProperty({
    description: 'Unique identifier of the preferences record',
    type: String,
    example: 'f47ac10b-58cc-4372-a567-0e02b2c3d479',
  })
  id: string;

  @ApiProperty({
    description: 'User ID reference',
    type: String,
    example: 'f47ac10b-58cc-4372-a567-0e02b2c3d479',
  })
  user_id: string;

  @ApiProperty({
    description: 'Preferred destination for OTP codes',
    enum: ['EMAIL', 'SMS', 'BOTH'],
    example: 'EMAIL',
  })
  otpDestination: 'EMAIL' | 'SMS' | 'BOTH';

  @ApiProperty({
    description: 'Whether to receive promotional communications',
    type: Boolean,
    example: true,
  })
  receivePromotions: boolean;

  @ApiProperty({
    description: 'Whether to enable or disable 2FA',
    type: Boolean,
    example: true,
  })
  enable2fa: boolean;

  @ApiProperty({
    description: 'General updates notification preferences',
    type: Boolean,
    example: true,
  })
  generalUpdates: boolean;

  @ApiProperty({
    description: 'Order updates notification preferences',
    type: Boolean,
    example: true,
  })
  orderUpdates: boolean;

  @ApiProperty({
    description: 'Transaction updates notification preferences',
    type: Boolean,
    example: false,
  })
  transactionUpdates: boolean;

  @ApiProperty({
    description: 'Payment updates notification preferences',
    type: Boolean,
    example: true,
  })
  paymentUpdates: boolean;

  @ApiProperty({
    description: 'Delivery updates notification preferences',
    type: Boolean,
    example: true,
  })
  deliveryUpdates: boolean;

  @ApiProperty({
    description: 'Creation timestamp',
    type: Date,
    example: '2023-03-01T12:00:00.000Z',
  })
  created_at: Date;

  @ApiProperty({
    description: 'Last update timestamp',
    type: Date,
    example: '2023-03-02T14:30:00.000Z',
  })
  updated_at: Date;
}
