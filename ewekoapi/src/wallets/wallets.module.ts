import { Module } from '@nestjs/common';
import { WalletsService } from './wallets.service';
import { WalletsController } from './wallets.controller';
import { TypeOrmModule } from '@nestjs/typeorm';
import { User, Wallet, Farmer } from '../users/entities/user.entity';

@Module({
  imports: [
    TypeOrmModule.forFeature([User, Wallet, Farmer]),
  ],
  controllers: [WalletsController],
  providers: [WalletsService],
  exports: [WalletsService, TypeOrmModule],
})
export class WalletsModule {}
