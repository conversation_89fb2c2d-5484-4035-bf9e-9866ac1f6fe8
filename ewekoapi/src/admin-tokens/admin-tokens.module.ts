import { Module } from '@nestjs/common';
import { AdminTokensService } from './admin-tokens.service';
import { AdminTokensController } from './admin-tokens.controller';
import { TypeOrmModule } from '@nestjs/typeorm';
import { User, Admin } from '../users/entities/user.entity';

// Note: AdminToken entity would need to be created in user.entity.ts if needed
@Module({
  imports: [
    TypeOrmModule.forFeature([User, Admin]),
  ],
  controllers: [AdminTokensController],
  providers: [AdminTokensService],
  exports: [AdminTokensService, TypeOrmModule],
})
export class AdminTokensModule {}
