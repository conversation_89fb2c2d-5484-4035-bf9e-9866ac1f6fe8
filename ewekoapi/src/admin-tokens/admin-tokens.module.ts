import { Modu<PERSON> } from '@nestjs/common';
import { AdminTokensService } from './admin-tokens.service';
import { AdminTokensController } from './admin-tokens.controller';
import { TypeOrmModule } from '@nestjs/typeorm';
import { User, Admin, AdminToken, Buyer, Farmer, PhoneNumber } from '../users/entities/user.entity';
import { UsersService } from 'src/users/users.service';

@Module({
  imports: [
    TypeOrmModule.forFeature([User, Admin, AdminToken, Buyer, Farmer, PhoneNumber]),
  ],
  controllers: [AdminTokensController],
  providers: [AdminTokensService, UsersService],
  exports: [AdminTokensService, TypeOrmModule],
})
export class AdminTokensModule {}
