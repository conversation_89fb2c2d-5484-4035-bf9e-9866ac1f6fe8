import { NestFactory } from '@nestjs/core';
import { AppModule } from './app.module';
import * as Sentry from '@sentry/nestjs';
import { nodeProfilingIntegration } from '@sentry/profiling-node';
import { SwaggerModule, DocumentBuilder } from '@nestjs/swagger';
import * as compression from 'compression';
import * as cookieParser from 'cookie-parser';
import { ValidationPipe } from '@nestjs/common';
import { NestExpressApplication } from '@nestjs/platform-express';
import * as express from 'express';
import { ConfigService } from '@nestjs/config';
import { SentryExceptionFilter } from 'src/shared/filters/sentry-exceptions.filter';
import helmet from 'helmet';
import { AllExceptionsFilter } from 'src/shared/filters/all-exception-filter.';

async function bootstrap() {
  const app = await NestFactory.create<NestExpressApplication>(AppModule);
  const configService = app.get(ConfigService);
  const port = configService.getOrThrow<number>('PORT');
  const baseUrl = configService.getOrThrow<string>('BASE_URL');

  Sentry.init({
    dsn: configService.getOrThrow('SENTRY_DSN'),
    integrations: [nodeProfilingIntegration()],
    environment: process.env.NODE_ENV || 'development',
    debug: true,
    tracesSampleRate: 1.0,
    profilesSampleRate: 1.0,
    release: undefined,
  });

  const config = new DocumentBuilder()
    .setTitle('Eweko API')
    .setDescription('The Eweko Backend API v2')
    .setVersion('2.0')
    .addBearerAuth()
    .build();
  const document = SwaggerModule.createDocument(app, config);
  document.servers = [{ url: '/api/v2' }];
  SwaggerModule.setup('api/v2/docs', app, document);

  // Updated CORS configuration
  app.enableCors({
    origin: [
      'http://localhost:3000',
      'http://localhost:3001',
      'https://www.ewekoaggregate.com',
      'https://ewekoaggregate.com',
      'https://www.staging.ewekoaggregate.com',
      'https://staging.ewekoaggregate.com',
      'https://253c-105-113-73-170.ngrok-free.app',
    ],
    methods: ['GET', 'POST', 'PUT', 'PATCH', 'DELETE', 'OPTIONS'],
    allowedHeaders: [
      'Content-Type',
      'Content-Length',
      'Cookie',
      'Referer',
      'User-Agent',
      'Authorization',
      'X-Requested-With',
      'Origin',
      'Accept',
      'Access-Control-Allow-Origin', // Added this header
      'Access-Control-Allow-Credentials', // Added this header
    ],
    exposedHeaders: ['Authorization'],
    credentials: true,
    preflightContinue: false,
    optionsSuccessStatus: 204,
    maxAge: 86400,
  });

  // For development, you might want to log CORS issues
  if (process.env.NODE_ENV === 'development') {
    app.use((req, res, next) => {
      console.log('Request Origin:', req.headers.origin);
      next();
    });
  }

  app.setGlobalPrefix('api/v2');
  app.useGlobalFilters(new SentryExceptionFilter(), new AllExceptionsFilter());

  // Updated helmet configuration to work with CORS
  app.use(
    helmet({
      crossOriginResourcePolicy: { policy: 'cross-origin' },
      crossOriginOpenerPolicy: { policy: 'same-origin' },
    }),
  );

  app.use(express.urlencoded({ extended: true }));
  app.use(express.json());
  app.use(compression());
  app.use(cookieParser());

  app.useGlobalPipes(
    new ValidationPipe({
      transform: true,
      whitelist: true,
    }),
  );

  // Serve static files from the public directory
  app.use('/products/images', express.static('public/product_images'));
  app.use('/downloads', express.static('public/downloads'));
  app.use('/', express.static('public/general'));

  await app.listen(port);

  console.log('-------------------------------');
  console.log(`Running: ${baseUrl}:${port}`);
  console.log('-------------------------------');
}
bootstrap();
