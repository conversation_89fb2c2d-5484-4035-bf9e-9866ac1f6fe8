import { Modu<PERSON> } from '@nestjs/common';
import { OrdersService } from './orders.service';
import { OrdersController } from './orders.controller';
import { TypeOrmModule } from '@nestjs/typeorm';
import { User, Order, Produce } from '../users/entities/user.entity';
import { UsersService } from 'src/users/users.service';
import { PaginationService } from 'src/shared/pagination/pagination.service';

@Module({
  imports: [
    TypeOrmModule.forFeature([User, Order, Produce]),
  ],
  controllers: [OrdersController],
  providers: [OrdersService, UsersService, PaginationService],
  exports: [OrdersService, TypeOrmModule],
})
export class OrdersModule {}
