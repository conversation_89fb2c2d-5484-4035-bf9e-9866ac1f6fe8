import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Query,
  UseGuards,
  NotFoundException,
  HttpCode,
  HttpStatus,
} from '@nestjs/common';
import { UsersService } from './users.service';
import {
  BuyerResponseDto,
  // BuyerDto,
  // CreateAdminDto,
  // CreateAgentDto,
  CreateBuyerDto,
  CreateFarmerDto,
  FarmerResponseDto,
  UserStatsDto,
  UserWithTypeDto,
  // CreateFarmerDto,
} from './dto/create-user.dto';
import {
  UpdateBuyerDto,
  UpdateFarmerDto,
  UpdateUserDto,
} from './dto/update-user.dto';
import {
  ApiBody,
  ApiOperation,
  ApiParam,
  ApiQuery,
  ApiResponse,
  ApiTags,
} from '@nestjs/swagger';
import { UserType } from 'src/shared/enums';
import { EwekoAuthGuard } from 'src/auth/jwt.guard';
import { CurrentUser } from 'src/shared/decorators/currentUser.decorator';
import {
  Buyer,
  Farmer,
} from './entities/user.entity';
import { validate as isUUID } from 'uuid';

@Controller('users')
@ApiTags('users')
@UseGuards(EwekoAuthGuard)
export class UsersController {
  constructor(private readonly usersService: UsersService) {}

  @Get('id/:userId')
  @ApiOperation({ summary: 'Find user by ID' })
  @ApiParam({ name: 'userId', description: 'User ID (MongoDB ObjectId)' })
  @ApiResponse({
    status: 200,
    description: 'User found',
    type: UserWithTypeDto,
  })
  @ApiResponse({ status: 404, description: 'User not found' })
  async findById(@Param('userId') userId: string): Promise<UserWithTypeDto> {
    return this.usersService.findById(userId);
  }

  @Get('username')
  @ApiOperation({ summary: 'Find user by username' })
  @ApiQuery({ name: 'username', required: true })
  @ApiResponse({
    status: 200,
    description: 'User found',
    type: UserWithTypeDto,
  })
  @ApiResponse({ status: 404, description: 'User not found' })
  async findUserByUsername(
    @Query('username') username: string,
  ): Promise<UserWithTypeDto> {
    return this.usersService.findUserByUsername(username);
  }

  @Get('email')
  @ApiOperation({ summary: 'Find user by email' })
  @ApiQuery({ name: 'email', required: true })
  @ApiResponse({
    status: 200,
    description: 'User found',
    type: UserWithTypeDto,
  })
  @ApiResponse({ status: 404, description: 'User not found' })
  async findUserByEmail(
    @Query('email') email: string,
  ): Promise<UserWithTypeDto> {
    return this.usersService.findUserByEmail(email);
  }

  @Get('phone')
  @ApiOperation({ summary: 'Find user by phone number' })
  @ApiQuery({ name: 'phoneNumber', required: true })
  @ApiResponse({
    status: 200,
    description: 'User found',
    type: UserWithTypeDto,
  })
  @ApiResponse({ status: 404, description: 'User not found' })
  async findUserByPhone(
    @Query('phoneNumber') phoneNumber: string,
  ): Promise<UserWithTypeDto> {
    return this.usersService.findUserByPhone(phoneNumber);
  }

  @Post('buyers')
  @ApiOperation({ summary: 'Register a new buyer user' })
  @ApiBody({
    description: 'Buyer registration data',
    type: CreateBuyerDto,
  })
  @ApiResponse({
    status: 201,
    description: 'Buyer successfully created',
    type: BuyerResponseDto,
  })
  @ApiResponse({
    status: 400,
    description: 'Invalid input data or validation failure',
  })
  async createBuyer(@Body() createBuyerDto: CreateBuyerDto): Promise<Buyer> {
    return this.usersService.createBuyer(createBuyerDto);
  }

  @Get('buyers')
  @ApiOperation({ summary: 'Get all buyers' })
  @ApiResponse({
    status: 200,
    description: 'List of all buyers',
    type: [BuyerResponseDto],
  })
  async findAllBuyers(): Promise<BuyerDocument[]> {
    return await this.usersService.findAllBuyers();
  }

  @Get('buyers/:id')
  @ApiOperation({ summary: 'Get a single buyer by ID' })
  @ApiParam({
    name: 'id',
    description: 'MongoDB ObjectId of the buyer',
    type: String,
  })
  @ApiResponse({
    status: 200,
    description: 'Single buyer details',
    type: BuyerResponseDto,
  })
  @ApiResponse({ status: 404, description: 'Buyer not found' })
  async findBuyerById(@Param('id') id: string): Promise<BuyerDocument> {
    if (!isUUID(id)) {
      throw new NotFoundException('Invalid Buyer ID');
    }

    const buyer = await this.usersService.findBuyerById(id);
    if (!buyer) {
      throw new NotFoundException('Buyer not found');
    }

    return buyer;
  }

  @Patch('buyers/:id')
  @ApiOperation({ summary: 'Update a buyer by ID' })
  @ApiParam({ name: 'id', description: 'The ID of the buyer to update' })
  @ApiBody({
    description: 'Fields to update in the buyer, profile, or contact',
    type: UpdateBuyerDto,
  })
  @ApiResponse({
    status: 200,
    description: 'Buyer successfully updated',
    type: BuyerResponseDto,
  })
  @ApiResponse({
    status: 404,
    description: 'Buyer not found',
  })
  async updateBuyer(
    @Param('id') buyerId: string,
    @Body() updateBuyerDto: UpdateBuyerDto,
  ): Promise<BuyerResponseDto> {
    const updated = await this.usersService.updateBuyer(
      buyerId,
      updateBuyerDto,
    );
    return updated as any; // You can transform to DTO if needed
  }

  @Delete('buyers/:id')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: 'Delete a buyer by ID' })
  @ApiParam({ name: 'id', description: 'The ID of the buyer to delete' })
  @ApiResponse({
    status: 200,
    description: 'Buyer successfully deleted',
    schema: {
      example: {
        message: 'Buyer successfully deleted',
      },
    },
  })
  @ApiResponse({
    status: 404,
    description: 'Buyer not found',
  })
  async deleteBuyer(
    @Param('id') buyerId: string,
  ): Promise<{ message: string }> {
    return this.usersService.deleteBuyer(buyerId);
  }

  @Post('farmers')
  @ApiOperation({ summary: 'Register a new farmer user' })
  @ApiBody({
    description: 'Farmer registration data',
    type: CreateFarmerDto,
  })
  @ApiResponse({
    status: 201,
    description: 'Farmer successfully created',
    type: FarmerResponseDto,
  })
  @ApiResponse({
    status: 400,
    description: 'Invalid input data or validation failure',
  })
  async createFarmer(
    @Body() createFarmerDto: CreateFarmerDto,
  ): Promise<Farmer> {
    return this.usersService.createFarmer(createFarmerDto);
  }

  @Get('farmers')
  @ApiOperation({ summary: 'Get all farmers' })
  @ApiResponse({
    status: 200,
    description: 'List of all farmers',
    type: [FarmerResponseDto],
  })
  async findAllFarmers(): Promise<FarmerDocument[]> {
    return await this.usersService.findAllFarmers();
  }

  @Get('farmers/:id')
  @ApiOperation({ summary: 'Get a single farmer by ID' })
  @ApiParam({
    name: 'id',
    description: 'MongoDB ObjectId of the farmer',
    type: String,
  })
  @ApiResponse({
    status: 200,
    description: 'Single farmer details',
    type: FarmerResponseDto,
  })
  @ApiResponse({ status: 404, description: 'Farmer not found' })
  async findFarmerById(@Param('id') id: string): Promise<FarmerDocument> {
    if (!isUUID(id)) {
      throw new NotFoundException('Invalid Farmer ID');
    }

    const farmer = await this.usersService.findFarmerById(id);
    if (!farmer) {
      throw new NotFoundException('Farmer not found');
    }

    return farmer;
  }

  @Patch('farmers/:id')
  @ApiOperation({ summary: 'Update a farmer by ID' })
  @ApiParam({ name: 'id', description: 'The ID of the farmer to update' })
  @ApiBody({
    description: 'Fields to update in the farmer, profile, or contact',
    type: UpdateFarmerDto,
  })
  @ApiResponse({
    status: 200,
    description: 'Farmer successfully updated',
    type: FarmerResponseDto,
  })
  @ApiResponse({
    status: 404,
    description: 'Farmer not found',
  })
  async updateFarmer(
    @Param('id') farmerId: string,
    @Body() updateFarmerDto: UpdateFarmerDto,
  ): Promise<FarmerResponseDto> {
    const updated = await this.usersService.updateFarmer(
      farmerId,
      updateFarmerDto,
    );
    return updated as any; // You can transform to DTO if needed
  }

  @Delete('farmers/:id')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: 'Delete a farmer by ID' })
  @ApiParam({ name: 'id', description: 'The ID of the farmer to delete' })
  @ApiResponse({
    status: 200,
    description: 'Farmer successfully deleted',
    schema: {
      example: {
        message: 'Farmer successfully deleted',
      },
    },
  })
  @ApiResponse({
    status: 404,
    description: 'Farmer not found',
  })
  async deleteFarmer(
    @Param('id') farmerId: string,
  ): Promise<{ message: string }> {
    return this.usersService.deleteFarmer(farmerId);
  }

  // @Get(':id')
  // @ApiOperation({ summary: 'Find user by ID (buyer or farmer)' })
  // @ApiParam({ name: 'id', description: 'MongoDB ObjectId of the user' })
  // @ApiResponse({
  //   status: 200,
  //   description: 'User found',
  //   type: UserWithTypeDto,
  // })
  // @ApiResponse({ status: 404, description: 'User not found' })
  // async findById(@Param('id') id: string): Promise<UserWithTypeDto> {
  //   return this.usersService.findById(id);
  // }

  @Get('username/:username')
  @ApiOperation({ summary: 'Find user by username' })
  @ApiParam({ name: 'username', description: 'Username to search for' })
  @ApiResponse({ status: 200, type: UserWithTypeDto })
  @ApiResponse({ status: 404, description: 'User not found' })
  async findByUsername(
    @Param('username') username: string,
  ): Promise<UserWithTypeDto> {
    return this.usersService.findUserByUsername(username);
  }

  @Get('email/:email')
  @ApiOperation({ summary: 'Find user by email' })
  @ApiParam({ name: 'email', description: 'Email address to search for' })
  @ApiResponse({ status: 200, type: UserWithTypeDto })
  @ApiResponse({ status: 404, description: 'User not found' })
  async findByEmail(@Param('email') email: string): Promise<UserWithTypeDto> {
    return this.usersService.findUserByEmail(email);
  }

  @Get('phone/:phone')
  @ApiOperation({ summary: 'Find user by phone number' })
  @ApiParam({
    name: 'phone',
    description: 'Primary phone number to search for',
  })
  @ApiResponse({ status: 200, type: UserWithTypeDto })
  @ApiResponse({ status: 404, description: 'User not found' })
  async findByPhone(@Param('phone') phone: string): Promise<UserWithTypeDto> {
    return this.usersService.findUserByPhone(phone);
  }

  @Get()
  @ApiOperation({ summary: 'Get all users (paginated)' })
  @ApiQuery({
    name: 'skip',
    required: false,
    type: Number,
    description: 'Number of records to skip',
  })
  @ApiQuery({
    name: 'limit',
    required: false,
    type: Number,
    description: 'Max number of records to return',
  })
  async findAllUsers(@Query('skip') skip = 0, @Query('limit') limit = 50) {
    return this.usersService.findAllUsers({ skip, limit });
  }

  @Get('search')
  @ApiOperation({
    summary: 'Search users by keyword (username, name, email, phone)',
  })
  @ApiQuery({ name: 'q', required: true, description: 'Search term (text)' })
  @ApiQuery({ name: 'skip', required: false, type: Number })
  @ApiQuery({ name: 'limit', required: false, type: Number })
  async searchUsers(
    @Query('q') q: string,
    @Query('skip') skip = 0,
    @Query('limit') limit = 50,
  ) {
    return this.usersService.searchUsers(q, { skip, limit });
  }

  @Get('stats')
  @ApiOperation({ summary: 'Get user statistics' })
  @ApiResponse({ status: 200, type: UserStatsDto })
  async getUserStats() {
    return this.usersService.getUserStats();
  }

  // @Get('all')
  // @ApiOperation({
  //   summary: 'Retrieve all users',
  //   description: 'Fetches all users across different user types.',
  // })
  // @ApiResponse({
  //   status: 200,
  //   description: 'All users retrieved successfully.',
  //   schema: {
  //     type: 'object',
  //     properties: {
  //       agents: { type: 'array', items: { type: 'object' } },
  //       admins: { type: 'array', items: { type: 'object' } },
  //       buyers: { type: 'array', items: { type: 'object' } },
  //       farmers: { type: 'array', items: { type: 'object' } },
  //     },
  //   },
  // })
  // @ApiResponse({
  //   status: 500,
  //   description: 'Server error occurred.',
  // })
  // async findAll() {
  //   return this.usersService.findAll();
  // }

  // @Get('email')
  // @ApiOperation({
  //   summary: 'Find user by email',
  //   description:
  //     'Retrieves a user by their email address, searching across all user types',
  // })
  // @ApiQuery({
  //   name: 'email',
  //   description: 'Email address of the user',
  //   type: String,
  //   example: '<EMAIL>',
  //   required: true,
  // })
  // @ApiResponse({
  //   status: 200,
  //   description: 'User found successfully',
  //   schema: {
  //     properties: {
  //       _id: { type: 'string', example: '676d54151987239244cae5a9' },
  //       firstName: { type: 'string', example: 'John' },
  //       lastName: { type: 'string', example: 'Doe' },
  //       email: { type: 'string', example: '<EMAIL>' },
  //       phoneNumber: { type: 'string', example: '+2348034023726' },
  //       userType: {
  //         type: 'string',
  //         enum: ['BUYER', 'FARMER', 'AGENT', 'ADMIN'],
  //       },
  //       addresses: { type: 'array', items: { type: 'object' } },
  //       notifications: { type: 'array', items: { type: 'object' } },
  //       otps: { type: 'array', items: { type: 'object' } },
  //     },
  //   },
  // })
  // @ApiResponse({
  //   status: 404,
  //   description: 'User not found',
  // })
  // async findByEmail(@Query('email') email: string) {
  //   return this.usersService.findByEmail(email);
  // }

  // @Get('phone')
  // @ApiOperation({
  //   summary: 'Find user by phone number',
  //   description:
  //     'Retrieves a user by their phone number, searching across all user types',
  // })
  // @ApiQuery({
  //   name: 'phoneNumber',
  //   description: 'Phone number of the user with country code',
  //   type: String,
  //   example: '+2348034023726',
  //   required: true,
  // })
  // @ApiResponse({
  //   status: 200,
  //   description: 'User found successfully',
  //   schema: {
  //     properties: {
  //       _id: { type: 'string', example: '676d54151987239244cae5a9' },
  //       firstName: { type: 'string', example: 'John' },
  //       lastName: { type: 'string', example: 'Doe' },
  //       email: { type: 'string', example: '<EMAIL>' },
  //       phoneNumber: { type: 'string', example: '+2348034023726' },
  //       userType: {
  //         type: 'string',
  //         enum: ['BUYER', 'FARMER', 'AGENT', 'ADMIN'],
  //       },
  //       addresses: { type: 'array', items: { type: 'object' } },
  //       notifications: { type: 'array', items: { type: 'object' } },
  //       otps: { type: 'array', items: { type: 'object' } },
  //     },
  //   },
  // })
  // @ApiResponse({
  //   status: 404,
  //   description: 'User not found',
  // })
  // async findByPhoneNumber(@Query('phoneNumber') phoneNumber: string) {
  //   return this.usersService.findByPhoneNumber(phoneNumber);
  // }

  // @Get('exists')
  // @ApiOperation({
  //   summary: 'Check if user exists',
  //   description:
  //     'Checks for user existence by ID, email, or phone number (at least one parameter required)',
  // })
  // @ApiQuery({
  //   name: 'id',
  //   description: 'ID of the user',
  //   type: String,
  //   required: true,
  // })
  // @ApiResponse({
  //   status: 200,
  //   description: 'User existence check completed',
  //   schema: {
  //     type: 'boolean',
  //     example: true,
  //   },
  // })
  // @ApiResponse({
  //   status: 400,
  //   description: 'Bad Request - No search parameters provided',
  // })
  // async userExists(@Query('id') identifier: string) {
  //   return this.usersService.userExists(identifier);
  // }

  // @Get(':id')
  // @ApiOperation({
  //   summary: 'Find user by ID',
  //   description:
  //     'Retrieves a user by their ID, searching across all user types (Buyer, Farmer, Agent, Admin)',
  // })
  // @ApiParam({
  //   name: 'id',
  //   description: 'ID of the user',
  //   type: String,
  //   example: '676d54151987239244cae5a9',
  // })
  // @ApiResponse({
  //   status: 200,
  //   description: 'User found successfully',
  // })
  // @ApiResponse({
  //   status: 404,
  //   description: 'User not found or invalid ID format',
  // })
  // async findById(@Param('id') id: string) {
  //   return this.usersService.findById(id);
  // }

  // @Patch(':id')
  // @ApiOperation({
  //   summary: 'Update user details',
  //   description: 'Updates user details by ID',
  // })
  // @ApiParam({
  //   name: 'id',
  //   description: 'The ID of the user to update',
  //   type: String,
  //   example: '676d54151987239244cae5a9',
  // })
  // @ApiResponse({
  //   status: 200,
  //   description: 'User details updated successfully',
  //   type: UpdateUserDto,
  // })
  // @ApiResponse({
  //   status: 404,
  //   description: 'User not found',
  // })
  // async update(@Param('id') id: string, @Body() updateUserDto: UpdateUserDto) {
  //   return this.usersService.update(id, updateUserDto);
  // }

  // @Delete(':id')
  // @ApiOperation({
  //   summary: 'Delete user by ID',
  //   description: 'Deletes a user by their ID across all user types.',
  // })
  // @ApiParam({
  //   name: 'id',
  //   description: 'The ID of the user to be deleted',
  //   type: String,
  //   example: '676d54151987239244cae5a9',
  // })
  // @ApiResponse({
  //   status: 200,
  //   description: 'User deleted successfully.',
  //   schema: {
  //     properties: {
  //       message: {
  //         type: 'string',
  //         example: 'User with ID 676d54151987239244cae5a9 deleted successfully',
  //       },
  //     },
  //   },
  // })
  // @ApiResponse({
  //   status: 404,
  //   description: 'User not found.',
  // })
  // async delete(@Param('id') id: string) {
  //   return this.usersService.delete(id);
  // }

  @Get('farmer/dashboard')
  async getFarmerDashboard(@CurrentUser('id') farmerId: string) {
    return this.usersService.getFarmerDashboard(farmerId);
  }
}
