import { IsString, <PERSON>Email, IsEnum, IsOptional, IsBoolean, IsDateString } from 'class-validator';
import { UserType } from '../../shared/enums';

export class CreateUserDto {
  // Authentication fields
  @IsString()
  username: string;

  @IsString()
  password: string;

  @IsEnum(UserType)
  type: UserType;

  @IsOptional()
  @IsBoolean()
  verified?: boolean;

  @IsOptional()
  @IsBoolean()
  is_active?: boolean;

  // Profile fields (now in main users table)
  @IsString()
  first_name: string;

  @IsString()
  last_name: string;

  @IsOptional()
  @IsString()
  middle_name?: string;

  @IsOptional()
  @IsString()
  prefix?: string;

  @IsOptional()
  @IsString()
  gender?: string;

  @IsOptional()
  @IsDateString()
  date_of_birth?: Date;

  @IsOptional()
  @IsString()
  profile_picture?: string;

  // Contact fields - email only (phones and addresses in separate tables)
  @IsEmail()
  email: string;

  // Business fields (now in main users table)
  @IsOptional()
  @IsString()
  business_name?: string;

  // Common fields
  @IsOptional()
  @IsBoolean()
  is_premium?: boolean;

  @IsOptional()
  @IsBoolean()
  is_phone_verified?: boolean;

  @IsOptional()
  @IsBoolean()
  is_email_verified?: boolean;
}

// DTOs for additional phone numbers and addresses
export class CreatePhoneNumberDto {
  @IsString()
  phone_number: string;

  @IsOptional()
  @IsString()
  type?: string; // 'personal', 'business', 'emergency', 'other'

  @IsOptional()
  @IsBoolean()
  is_primary?: boolean;
}

export class CreateAddressDto {
  @IsString()
  street: string;

  @IsString()
  city: string;

  @IsString()
  state: string;

  @IsString()
  country: string;

  @IsOptional()
  @IsString()
  postal_code?: string;

  @IsOptional()
  @IsString()
  type?: string; // 'home', 'work', 'delivery', 'billing', 'other'

  @IsOptional()
  @IsBoolean()
  is_primary?: boolean;
}

// Backward compatibility DTOs
export class CreateBuyerDto extends CreateUserDto {
  // Frontend compatibility fields
  @IsOptional()
  @IsString()
  firstName?: string; // Maps to first_name

  @IsOptional()
  @IsString()
  lastName?: string; // Maps to last_name

  @IsOptional()
  @IsString()
  primaryPhone?: string; // Primary phone number

  constructor() {
    super();
    this.type = UserType.BUYER;
  }
}

export class CreateFarmerDto extends CreateUserDto {
  // Frontend compatibility fields
  @IsOptional()
  @IsString()
  firstName?: string; // Maps to first_name

  @IsOptional()
  @IsString()
  lastName?: string; // Maps to last_name

  @IsOptional()
  @IsString()
  primaryPhone?: string; // Primary phone number

  @IsOptional()
  @IsString()
  farmName?: string; // Maps to business_name

  constructor() {
    super();
    this.type = UserType.FARMER;
  }
}

// Response DTOs
export class BuyerResponseDto {
  id: string;
  username: string;
  type: UserType;
  first_name: string;
  last_name: string;
  email: string;
  phone_numbers?: CreatePhoneNumberDto[];
  addresses?: CreateAddressDto[];
}

export class FarmerResponseDto {
  id: string;
  username: string;
  type: UserType;
  first_name: string;
  last_name: string;
  email: string;
  phone_numbers?: CreatePhoneNumberDto[];
  addresses?: CreateAddressDto[];
}

export class UserStatsDto {
  totalUsers: number;
  totalBuyers: number;
  totalFarmers: number;
  totalAdmins: number;
}

export class UserWithTypeDto {
  id: string;
  username: string;
  type: UserType;
  verified: boolean;
  is_active: boolean;
  first_name: string;
  last_name: string;
  email: string;
  phone_numbers?: CreatePhoneNumberDto[];
  addresses?: CreateAddressDto[];
}
