import { Modu<PERSON> } from '@nestjs/common';
import { UsersService } from './users.service';
import { UsersController } from './users.controller';
import { TypeOrmModule } from '@nestjs/typeorm';

import {
  User,
  Farmer,
  Buyer,
  Admin,
  PhoneNumber,
  Address,
  Notification
} from './entities/user.entity';
import { Wallet } from '../wallets/entities/wallet.entity';
import { Order } from '../orders/entities/order.entity';
import { Produce } from '../produce/entities/produce.entity';
import { Cart } from '../cart/entities/cart.entity';
import { Preferences } from '../preferences/entities/preferences.entity';
import { WalletsService } from 'src/wallets/wallets.service';
import { CartService } from 'src/cart/cart.service';
import { PreferencesService } from 'src/preferences/preferences.service';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      User,
      Farmer,
      Buyer,
      Admin,
      PhoneNumber,
      Address,
      Notification,
      Wallet,
      Order,
      Produce,
      Cart,
      Preferences,
    ]),
  ],
  controllers: [UsersController],
  providers: [UsersService, WalletsService, CartService, PreferencesService],
  exports: [UsersService, TypeOrmModule],
})
export class UsersModule {}
