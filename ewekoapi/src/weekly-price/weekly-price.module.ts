import { Module } from '@nestjs/common';
import { ScheduleModule } from '@nestjs/schedule';
import { TypeOrmModule } from '@nestjs/typeorm';
import { WeeklyPriceService } from './weekly-price.service';
import { WeeklyPriceController } from './weekly-price.controller';
import { Category } from '../users/entities/user.entity';
import { CategoryModule } from '../category/category.module';

// Note: WeeklyCategoryPrice entity would need to be created in user.entity.ts
@Module({
  imports: [
    ScheduleModule.forRoot(),
    TypeOrmModule.forFeature([Category]),
    CategoryModule,
  ],
  controllers: [WeeklyPriceController],
  providers: [WeeklyPriceService],
  exports: [WeeklyPriceService, TypeOrmModule],
})
export class WeeklyPriceModule {}
