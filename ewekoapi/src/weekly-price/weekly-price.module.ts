import { Module } from '@nestjs/common';
import { ScheduleModule } from '@nestjs/schedule';
import { TypeOrmModule } from '@nestjs/typeorm';
import { WeeklyPriceService } from './weekly-price.service';
import { WeeklyPriceController } from './weekly-price.controller';
import { WeeklyCategoryPrice } from '../users/entities/user.entity';
import { Category } from '../category/entities/category.entity';
import { Produce } from '../produce/entities/produce.entity';
import { CategoryModule } from '../category/category.module';

@Module({
  imports: [
    ScheduleModule.forRoot(),
    TypeOrmModule.forFeature([WeeklyCategoryPrice, Category, Produce]),
    CategoryModule,
  ],
  controllers: [WeeklyPriceController],
  providers: [WeeklyPriceService],
  exports: [WeeklyPriceService, TypeOrmModule],
})
export class WeeklyPriceModule {}
