import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { WeeklyCategoryPrice } from '../users/entities/user.entity';
import { Category } from '../category/entities/category.entity';
import { Produce } from '../produce/entities/produce.entity';
import {
  CategoryWeeklyPriceDto,
  WeeklyPricesResponse
} from './dto/weekly-price.dto';

@Injectable()
export class WeeklyPriceService {
  private readonly logger = new Logger(WeeklyPriceService.name);

  constructor(
    @InjectRepository(WeeklyCategoryPrice)
    private readonly weeklyPriceRepository: Repository<WeeklyCategoryPrice>,
    @InjectRepository(Category)
    private readonly categoryRepository: Repository<Category>,
    @InjectRepository(Produce)
    private readonly produceRepository: Repository<Produce>,
  ) {}

  /**
   * Get the start of the week (Monday) for a given date
   * This ensures consistent weekly buckets for price aggregation
   */
  private getStartOfWeek(date: Date = new Date()): Date {
    const d = new Date(date);
    const day = d.getUTCDay();
    const diff = d.getUTCDate() - day + (day === 0 ? -6 : 1); // Adjust when day is Sunday
    d.setUTCDate(diff);
    d.setUTCHours(0, 0, 0, 0);
    return d;
  }

  /**
   * Calculate and store weekly price averages for all categories
   * This is called by the CronsService on a weekly schedule
   */
  async calculateWeeklyAverages() {
    const weekStart = this.getStartOfWeek();
    this.logger.log(`Starting weekly price calculation for week starting ${weekStart.toISOString()}`);

    try {
      // Get all categories with debug info
      const categories = await this.categoryRepository.find();
      this.logger.debug(`Found ${categories.length} categories to process`);

      // Calculate average price for each category
      for (const category of categories) {
        this.logger.debug(`Processing category: ${category.name} (${category.id})`);

        try {
          // Get active produces for this category
          const activeProduces = await this.produceRepository.find({
            where: {
              category_id: category.id,
              quantity: { $gt: 0 } as any,
              is_available: true
            },
            select: ['id', 'name', 'price']
          });

          this.logger.debug(`Found ${activeProduces.length} active produces for category ${category.name}`);

          if (activeProduces.length > 0) {
            // Log some sample produces for debugging
            activeProduces.slice(0, 3).forEach((p, i) => {
              this.logger.debug(`Produce ${i + 1}: ${p.name} - ${p.price}`);
            });
            if (activeProduces.length > 3) {
              this.logger.debug(`... and ${activeProduces.length - 3} more produces`);
            }

            // Calculate average price
            const totalPrice = activeProduces.reduce((sum, p) => sum + Number(p.price), 0);
            const averagePrice = totalPrice / activeProduces.length;
            const roundedAvg = Math.round(averagePrice * 100) / 100;

            // Calculate min and max prices
            const prices = activeProduces.map(p => Number(p.price));
            const minPrice = Math.min(...prices);
            const maxPrice = Math.max(...prices);

            // Get week end date
            const weekEnd = new Date(weekStart);
            weekEnd.setDate(weekEnd.getDate() + 6);

            // Update or create the weekly price record
            const existingRecord = await this.weeklyPriceRepository.findOne({
              where: {
                category_id: category.id,
                week_start: weekStart
              }
            });

            if (existingRecord) {
              await this.weeklyPriceRepository.update(existingRecord.id, {
                avg_price: roundedAvg,
                min_price: minPrice,
                max_price: maxPrice
              });
            } else {
              await this.weeklyPriceRepository.save({
                category_id: category.id,
                week_start: weekStart,
                week_end: weekEnd,
                avg_price: roundedAvg,
                min_price: minPrice,
                max_price: maxPrice
              });
            }

            this.logger.log(`✅ Updated weekly price for category "${category.name}": ${roundedAvg} (${activeProduces.length} listings)`);
          } else {
            this.logger.warn(`⚠️ No active produces found for category "${category.name}" (${category.id})`);

            // Debug: Check if there are any produces (active or inactive) for this category
            const anyProduces = await this.produceRepository.find({
              where: { category_id: category.id },
              select: ['id', 'name', 'is_available']
            });

            if (anyProduces.length > 0) {
              this.logger.debug(`Found ${anyProduces.length} total produces (including inactive) for category ${category.name}:`);
              anyProduces.slice(0, 5).forEach(p => {
                this.logger.debug(`- ${p.name} (${p.is_available ? 'available' : 'unavailable'})`);
              });
            } else {
              this.logger.debug(`No produces found at all for category ${category.name}`);
            }
          }
        } catch (error) {
          this.logger.error(
            `Error calculating weekly average for category ${category.name}: ${error.message}`,
            error.stack
          );
        }
      }
      
      this.logger.log('Weekly price calculation completed');
    } catch (error) {
      this.logger.error(`Error in weekly price calculation: ${error.message}`, error.stack);
    }
  }

  /**
   * Get the latest weekly prices for all categories
   * Returns current and previous week's prices
   */
  async getLatestWeeklyPrices(): Promise<WeeklyPricesResponse> {
    const currentWeekStart = this.getStartOfWeek();
    const previousWeekStart = new Date(currentWeekStart);
    previousWeekStart.setDate(previousWeekStart.getDate() - 7);

    // Get all categories
    const categories = await this.categoryRepository.find();

    const categoryPrices: CategoryWeeklyPriceDto[] = [];

    for (const category of categories) {
      // Get current week price
      const currentWeekPrice = await this.weeklyPriceRepository.findOne({
        where: {
          category_id: category.id,
          week_start: currentWeekStart
        }
      });

      // Get previous week price
      const previousWeekPrice = await this.weeklyPriceRepository.findOne({
        where: {
          category_id: category.id,
          week_start: previousWeekStart
        }
      });

      const currentPrice = currentWeekPrice?.avg_price || 0;
      const previousPrice = previousWeekPrice?.avg_price || 0;

      let trend: 'up' | 'down' | 'stable' = 'stable';
      if (currentPrice > previousPrice) {
        trend = 'up';
      } else if (currentPrice < previousPrice) {
        trend = 'down';
      }

      categoryPrices.push({
        categoryId: category.id,
        categoryName: category.name,
        currentPrice,
        previousPrice,
        trend,
        minPrice: currentWeekPrice?.min_price || 0,
        maxPrice: currentWeekPrice?.max_price || 0
      });
    }

    return { data: categoryPrices };
  }
}
