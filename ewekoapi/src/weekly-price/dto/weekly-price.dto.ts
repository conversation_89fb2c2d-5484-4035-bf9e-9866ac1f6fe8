import { ApiProperty } from '@nestjs/swagger';
import { IsNumber, IsString } from 'class-validator';

export class CategoryWeeklyPriceDto {
  @ApiProperty({ description: 'Name of the category' })
  @IsString()
  categoryName: string;

  @ApiProperty({ description: 'Current week\'s average price' })
  @IsNumber()
  currentPrice: number;

  @ApiProperty({ description: 'Previous week\'s average price' })
  @IsNumber()
  previousPrice: number;
}

export class WeeklyPricesResponse {
  @ApiProperty({ 
    description: 'Array of weekly prices by category',
    type: [CategoryWeeklyPriceDto]
  })
  data: CategoryWeeklyPriceDto[];
}
