import {
  <PERSON><PERSON><PERSON>,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  OneToMany,
  JoinColumn,
} from 'typeorm';
import { Farmer } from '../../users/entities/user.entity';
import { Category } from '../../category/entities/category.entity';
import { CartItem } from '../../cart/entities/cart.entity';
import { OrderItem } from '../../orders/entities/order.entity';

@Entity('produce')
export class Produce {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ type: 'uuid' })
  farmer_id: string;

  @Column({ type: 'uuid' })
  category_id: string;

  @Column()
  name: string;

  @Column({ type: 'text', nullable: true })
  description: string;

  @Column({ type: 'decimal', precision: 10, scale: 2 })
  price: number;

  @Column()
  unit: string;

  @Column({ type: 'int' })
  quantity: number;

  @Column({ type: 'json', nullable: true })
  images: string[];

  @Column({ default: true })
  is_available: boolean;

  @Column({ default: true })
  is_active: boolean;

  @Column({ unique: true })
  slug: string;

  @Column({ type: 'date', nullable: true })
  harvest_date: Date;

  @Column({ type: 'date', nullable: true })
  expiry_date: Date;

  @CreateDateColumn()
  created_at: Date;

  @UpdateDateColumn()
  updated_at: Date;

  // Relationships
  @ManyToOne(() => Farmer, farmer => farmer.produce)
  @JoinColumn({ name: 'farmer_id' })
  farmer: Farmer;

  @ManyToOne(() => Category, category => category.produce)
  @JoinColumn({ name: 'category_id' })
  category: Category;

  @OneToMany(() => CartItem, cartItem => cartItem.produce)
  cart_items: CartItem[];

  @OneToMany(() => OrderItem, orderItem => orderItem.produce)
  order_items: OrderItem[];
}
