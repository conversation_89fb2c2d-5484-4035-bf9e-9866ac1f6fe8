import { Module } from '@nestjs/common';
import { ProduceService } from './produce.service';
import { ProduceController } from './produce.controller';
import { TypeOrmModule } from '@nestjs/typeorm';
import { User, Farmer, Produce, Category } from '../users/entities/user.entity';

@Module({
  imports: [
    TypeOrmModule.forFeature([User, Farmer, Produce, Category]),
  ],
  controllers: [ProduceController],
  providers: [ProduceService],
  exports: [ProduceService, TypeOrmModule],
})
export class ProduceModule {}
